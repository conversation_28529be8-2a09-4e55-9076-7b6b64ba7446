// RealisticDoorExternal.jsx
import React, { useEffect, useRef, useState } from 'react';
import { gsap } from 'gsap';
import FootstepsAnimation from './FootstepsAnimation'; // your existing component

const SVG_PATH = '/realistic_door_external.svg'; // served from public/

export default function RealisticDoorExternal({ year = '2019', committeeName = 'Executive Committee', onEnter, onOpenComplete }) {
  const containerRef = useRef(null);
  const leftRef = useRef(null);
  const rightRef = useRef(null);
  const [isWalking, setIsWalking] = useState(false);
  const [isOpening, setIsOpening] = useState(false);

  useEffect(() => {
    // fetch the SVG text and inline it so GSAP can find #leftDoor and #rightDoor
    let mounted = true;
    fetch(SVG_PATH)
      .then(r => r.text())
      .then(svgText => {
        if (!mounted || !containerRef.current) return;
        containerRef.current.innerHTML = svgText;

        // wire up hit click
        const hit = containerRef.current.querySelector('#hit');
        if (hit) hit.addEventListener('click', handleDoorClick);

        // grab refs to groups
        leftRef.current = containerRef.current.querySelector('#leftDoor');
        rightRef.current = containerRef.current.querySelector('#rightDoor');

        // ensure 3D transform and hide backface
        [leftRef.current, rightRef.current].forEach(el => {
          if (!el) return;
          el.style.transformStyle = 'preserve-3d';
          el.style.backfaceVisibility = 'hidden';
          el.style.webkitBackfaceVisibility = 'hidden';
          // Important: ensure transform-box so transformOrigin uses element bounds
          el.style.transformBox = 'fill-box';
          el.style.webkitTransformBox = 'fill-box';
        });
      })
      .catch(err => console.error('Failed to fetch SVG', err));

    return () => { mounted = false; };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const handleDoorClick = () => {
    if (isOpening) return;
    setIsOpening(true);
    setIsWalking(true);

    const tl = gsap.timeline({
      onComplete: () => {
        setIsOpening(false);
        setIsWalking(false);
        onOpenComplete?.();
      }
    });

    // realistic swing: rotateY nearly 90 degrees with easing
    tl.to(leftRef.current, { rotationY: -88, transformOrigin: '0% 50%', duration: 1.2, ease: 'power2.out' }, 0)
      .to(rightRef.current, { rotationY: 88, transformOrigin: '100% 50%', duration: 1.2, ease: 'power2.out' }, 0)
      .call(() => onEnter?.(), [], 0.8);

    // Footsteps animation will play while isWalking === true
  };

  return (
    <div style={{ width: 420, margin: '0 auto', perspective: 1100 }}>
      <div ref={containerRef} style={{ width: '100%', height: '600px', transformStyle: 'preserve-3d' }} />
      <FootstepsAnimation isWalking={isWalking} onWalkComplete={() => setIsWalking(false)} />
      <div style={{ textAlign: 'center', marginTop: 8, color: '#d4b27a', fontFamily: "Georgia, 'Times New Roman', serif" }}>
        {year} - {committeeName}
      </div>
    </div>
  );
}
