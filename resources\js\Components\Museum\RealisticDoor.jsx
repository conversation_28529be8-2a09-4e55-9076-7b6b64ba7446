// RealisticDoorExternal.jsx
import React, { useEffect, useRef, useState } from 'react';
import { gsap } from 'gsap';
import FootstepsAnimation from './FootstepsAnimation'; // your existing component

const SVG_PATH = '/realistic_door_external.svg'; // served from public/

export default function RealisticDoorExternal({ year = '2019', committeeName = 'Executive Committee', onEnter, onOpenComplete, isOpen }) {
  const containerRef = useRef(null);
  const leftRef = useRef(null);
  const rightRef = useRef(null);
  const [isWalking, setIsWalking] = useState(false);
  const [isOpening, setIsOpening] = useState(false);
  const [doorLoaded, setDoorLoaded] = useState(false);

  useEffect(() => {
    // fetch the SVG text and inline it so GSAP can find #leftDoor and #rightDoor
    let mounted = true;
    fetch(SVG_PATH)
      .then(r => r.text())
      .then(svgText => {
        if (!mounted || !containerRef.current) return;

        // Replace placeholder text with actual data
        const updatedSvg = svgText
          .replace(/{{YEAR}}/g, year)
          .replace(/{{COMMITTEE_NAME}}/g, committeeName);

        containerRef.current.innerHTML = updatedSvg;

        // wire up hit click
        const hit = containerRef.current.querySelector('#hit');
        if (hit) {
          hit.addEventListener('click', handleDoorClick);
          hit.style.cursor = 'pointer';
        }

        // grab refs to groups
        leftRef.current = containerRef.current.querySelector('#leftDoor');
        rightRef.current = containerRef.current.querySelector('#rightDoor');

        // ensure 3D transform and hide backface
        [leftRef.current, rightRef.current].forEach(el => {
          if (!el) return;
          el.style.transformStyle = 'preserve-3d';
          el.style.backfaceVisibility = 'hidden';
          el.style.webkitBackfaceVisibility = 'hidden';
          // Important: ensure transform-box so transformOrigin uses element bounds
          el.style.transformBox = 'fill-box';
          el.style.webkitTransformBox = 'fill-box';
          el.style.transition = 'transform 0.1s ease-out';
        });

        // Add hover effects
        const doorContainer = containerRef.current.querySelector('svg');
        if (doorContainer) {
          doorContainer.addEventListener('mouseenter', handleDoorHover);
          doorContainer.addEventListener('mouseleave', handleDoorLeave);
        }

        setDoorLoaded(true);
      })
      .catch(err => console.error('Failed to fetch SVG', err));

    return () => { mounted = false; };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [year, committeeName]);

  const handleDoorHover = () => {
    if (isOpening || isOpen) return;

    // Subtle hover animation - doors slightly open
    gsap.to(leftRef.current, { rotationY: -5, duration: 0.3, ease: 'power2.out' });
    gsap.to(rightRef.current, { rotationY: 5, duration: 0.3, ease: 'power2.out' });
  };

  const handleDoorLeave = () => {
    if (isOpening || isOpen) return;

    // Return to closed position
    gsap.to(leftRef.current, { rotationY: 0, duration: 0.3, ease: 'power2.out' });
    gsap.to(rightRef.current, { rotationY: 0, duration: 0.3, ease: 'power2.out' });
  };

  const handleDoorClick = () => {
    if (isOpening || isOpen) return;

    setIsOpening(true);
    setIsWalking(true);

    // Sound effect simulation
    const audioContext = new (window.AudioContext || window.webkitAudioContext)();
    const oscillator = audioContext.createOscillator();
    const gainNode = audioContext.createGain();

    oscillator.connect(gainNode);
    gainNode.connect(audioContext.destination);

    oscillator.frequency.setValueAtTime(200, audioContext.currentTime);
    oscillator.frequency.exponentialRampToValueAtTime(100, audioContext.currentTime + 0.5);
    gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
    gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.5);

    oscillator.start(audioContext.currentTime);
    oscillator.stop(audioContext.currentTime + 0.5);

    const tl = gsap.timeline({
      onComplete: () => {
        setIsOpening(false);
        onOpenComplete?.();
      }
    });

    // More realistic door opening with slight delay between doors
    tl.to(leftRef.current, {
      rotationY: -92,
      transformOrigin: '0% 50%',
      duration: 1.4,
      ease: 'power2.out'
    }, 0)
    .to(rightRef.current, {
      rotationY: 92,
      transformOrigin: '100% 50%',
      duration: 1.4,
      ease: 'power2.out'
    }, 0.1) // Slight delay for realism
    .call(() => {
      onEnter?.();
      // Start footsteps after doors are mostly open
      setTimeout(() => setIsWalking(false), 2000);
    }, [], 1.0);
  };

  return (
    <div className="relative" style={{ width: 450, margin: '0 auto', perspective: 1200 }}>
      {/* Door Container */}
      <div
        ref={containerRef}
        style={{
          width: '100%',
          height: '650px',
          transformStyle: 'preserve-3d',
          filter: isOpening ? 'brightness(1.2)' : 'brightness(1)'
        }}
      />

      {/* Loading State */}
      {!doorLoaded && (
        <div className="absolute inset-0 flex items-center justify-center bg-gradient-to-b from-amber-900/20 to-black/40 rounded-lg">
          <div className="text-amber-200 font-serif">Loading Hall...</div>
        </div>
      )}

      {/* Footsteps Animation - positioned relative to door */}
      {isWalking && (
        <div className="absolute inset-0 pointer-events-none">
          <FootstepsAnimation
            isWalking={isWalking}
            onWalkComplete={() => setIsWalking(false)}
          />
        </div>
      )}

      {/* Door Information */}
      <div className="text-center mt-4 px-4">
        <div className="bg-black/40 backdrop-blur-sm rounded-lg p-3 border border-amber-500/30">
          <div className="text-amber-200 text-lg font-serif font-bold">{year}</div>
          <div className="text-amber-300/80 text-sm font-serif italic">{committeeName}</div>
          {!isOpening && !isOpen && (
            <div className="text-amber-400/60 text-xs mt-2">Click to enter the hall</div>
          )}
          {isOpening && (
            <div className="text-amber-400 text-xs mt-2 animate-pulse">Opening doors...</div>
          )}
        </div>
      </div>
    </div>
  );
}
