import React, { useRef, useEffect } from 'react';
import { gsap } from 'gsap';

const FootstepsAnimation = ({ isWalking, onWalkComplete }) => {
  const containerRef = useRef(null);
  const footstepsRef = useRef([]);

  useEffect(() => {
    if (!isWalking) return;

    const container = containerRef.current;
    if (!container) return;

    // Clear existing footsteps
    container.innerHTML = '';
    footstepsRef.current = [];

    const footstepCount = 8;
    const footsteps = [];

    for (let i = 0; i < footstepCount; i++) {
      const footstep = document.createElement('img');

      // Alternate left/right
      const isLeftFoot = i % 2 === 0;
      footstep.src = isLeftFoot
        ? '/img/svg/left-foot.png'  // path inside public/
        : '/img/svg/right-foot.png';

      footstep.style.position = 'absolute';
      footstep.style.width = '40px';
      footstep.style.height = 'auto';

      // Walking pattern
      const progress = i / (footstepCount - 1);
      const x = 50 + progress * 200;
      const y = 80 + (isLeftFoot ? -10 : 10);

      footstep.style.left = `${x}px`;
      footstep.style.top = `${y}%`;
      footstep.style.opacity = '0';
      footstep.style.transform = 'scale(0.5)';

      container.appendChild(footstep);
      footsteps.push(footstep);
    }

    footstepsRef.current = footsteps;

    // Animation
    const tl = gsap.timeline({
      onComplete: () => {
        gsap.to(footsteps, {
          opacity: 0,
          duration: 1,
          stagger: 0.1,
          onComplete: onWalkComplete,
        });
      },
    });

    footsteps.forEach((footstep, index) => {
      tl.to(
        footstep,
        {
          opacity: 0.8,
          scale: 1,
          duration: 0.3,
          ease: 'power2.out',
        },
        index * 0.2
      ).to(
        footstep,
        {
          opacity: 0.4,
          duration: 0.2,
        },
        index * 0.2 + 0.3
      );
    });
  }, [isWalking, onWalkComplete]);

  return (
    <div
      ref={containerRef}
      className="absolute inset-0 pointer-events-none z-10"
      style={{ perspective: '1000px' }}
    />
  );
};

export default FootstepsAnimation;
