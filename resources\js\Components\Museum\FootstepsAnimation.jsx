import React, { useRef, useEffect } from 'react';
import { gsap } from 'gsap';

const FootstepsAnimation = ({ isWalking, onWalkComplete }) => {
  const containerRef = useRef(null);
  const footstepsRef = useRef([]);

  useEffect(() => {
    if (!isWalking) return;

    const container = containerRef.current;
    if (!container) return;

    // Clear existing footsteps
    container.innerHTML = '';
    footstepsRef.current = [];

    // Realistic walking: only 4-5 footsteps (2-3 steps)
    const footstepCount = 4;
    const footsteps = [];

    // Realistic walking pattern parameters
    const startX = 50; // Start from center
    const stepLength = 60; // Distance between steps
    const stepWidth = 25; // Distance between left and right foot
    const walkDirection = 45; // Walk towards the room at an angle

    for (let i = 0; i < footstepCount; i++) {
      const footstep = document.createElement('img');

      // Alternate left/right foot
      const isLeftFoot = i % 2 === 0;
      footstep.src = isLeftFoot
        ? '/img/svg/left-foot.png'
        : '/img/svg/right-foot.png';

      footstep.style.position = 'absolute';
      footstep.style.width = '32px';
      footstep.style.height = 'auto';
      footstep.style.zIndex = '20';

      // Realistic walking pattern - diagonal movement into the room
      const stepNumber = Math.floor(i / 2);
      const baseX = startX + (stepNumber * stepLength);
      const baseY = 75 - (stepNumber * 15); // Move up (into the room) as we walk

      // Offset for left/right foot
      const x = baseX + (isLeftFoot ? -stepWidth/2 : stepWidth/2);
      const y = baseY + (isLeftFoot ? -5 : 5);

      // Add slight rotation for natural foot angle
      const rotation = isLeftFoot ? -15 : 15;

      footstep.style.left = `${x}px`;
      footstep.style.top = `${y}%`;
      footstep.style.opacity = '0';
      footstep.style.transform = `scale(0.3) rotate(${rotation}deg)`;
      footstep.style.filter = 'drop-shadow(2px 2px 4px rgba(0,0,0,0.3))';

      container.appendChild(footstep);
      footsteps.push(footstep);
    }

    footstepsRef.current = footsteps;

    // Realistic walking animation - sequential footsteps
    const tl = gsap.timeline({
      onComplete: () => {
        // Fade out footsteps after a moment
        setTimeout(() => {
          gsap.to(footsteps, {
            opacity: 0,
            scale: 0.2,
            duration: 0.8,
            stagger: 0.1,
            ease: 'power2.in',
            onComplete: onWalkComplete,
          });
        }, 1000);
      },
    });

    // Animate each footstep appearing in sequence (like real walking)
    footsteps.forEach((footstep, index) => {
      const delay = index * 0.4; // 400ms between each step (realistic walking pace)

      tl.to(footstep, {
        opacity: 0.7,
        scale: 1,
        duration: 0.2,
        ease: 'power2.out',
      }, delay)
      .to(footstep, {
        opacity: 0.5,
        duration: 0.3,
        ease: 'power1.out',
      }, delay + 0.2);
    });

  }, [isWalking, onWalkComplete]);

  return (
    <div
      ref={containerRef}
      className="absolute inset-0 pointer-events-none z-10"
      style={{ perspective: '1000px' }}
    />
  );
};

export default FootstepsAnimation;
