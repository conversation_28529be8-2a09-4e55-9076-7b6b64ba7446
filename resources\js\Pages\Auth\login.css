/* Enhanced Login Animations & Styles */

/* Custom animations for the login page */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInFromLeft {
  from {
    opacity: 0;
    transform: translateX(-50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInFromRight {
  from {
    opacity: 0;
    transform: translateX(50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0, 0, 0);
  }
  40%, 43% {
    transform: translate3d(0, -30px, 0);
  }
  70% {
    transform: translate3d(0, -15px, 0);
  }
  90% {
    transform: translate3d(0, -4px, 0);
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes float {
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
  100% {
    transform: translateY(0px);
  }
}

@keyframes glow {
  0% {
    box-shadow: 0 0 5px rgba(147, 51, 234, 0.5);
  }
  50% {
    box-shadow: 0 0 20px rgba(147, 51, 234, 0.8), 0 0 30px rgba(147, 51, 234, 0.6);
  }
  100% {
    box-shadow: 0 0 5px rgba(147, 51, 234, 0.5);
  }
}

/* Apply animations */
.animate-fade-in {
  animation: fadeIn 0.6s ease-out;
}

.animate-slide-in-left {
  animation: slideInFromLeft 0.6s ease-out;
}

.animate-slide-in-right {
  animation: slideInFromRight 0.6s ease-out;
}

.animate-bounce {
  animation: bounce 2s infinite;
}

.animate-pulse {
  animation: pulse 2s infinite;
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-glow {
  animation: glow 2s ease-in-out infinite alternate;
}

/* Character-specific animations */
.character-container {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.character-typing {
  animation: float 2s ease-in-out infinite;
}

.character-hiding {
  animation: pulse 1.5s ease-in-out infinite;
}

/* Eye tracking animations */
.eyeL, .eyeR {
  transition: transform 0.2s ease-out;
  transform-origin: center;
}

.eye-following {
  animation: float 4s ease-in-out infinite;
}

/* Arms animation for password hiding */
.arms {
  transition: all 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

.arms-hiding {
  animation: slideInFromLeft 0.5s ease-out;
}

/* Input focus animations */
.input-focus {
  transform: scale(1.02);
  transition: all 0.3s ease;
}

/* Button hover effects */
.btn-hover {
  transition: all 0.3s ease;
}

.btn-hover:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

/* Floating elements */
.floating-element {
  position: absolute;
  pointer-events: none;
  animation: float 6s ease-in-out infinite;
}

.floating-element:nth-child(2) {
  animation-delay: -2s;
}

.floating-element:nth-child(3) {
  animation-delay: -4s;
}

/* Performance optimizations */
.gpu-accelerated {
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
}

/* Smooth transitions for all interactive elements */
* {
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}
