<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateCommitteesTable extends Migration
{
    public function up()
    {
        Schema::create('committee_years', function (Blueprint $table) {
            $table->id();
            $table->year('year');                    // Committee year (2020, 2021, etc.)
            $table->string('theme')->nullable();     // "Digital Innovation Era"
            $table->string('door_color', 7)->default('#8B5A2B');  // Hex color for door
            $table->string('room_mood')->default('amber');         // Room lighting mood
            $table->text('room_ambient')->nullable(); // Room description
            $table->string('door_sound')->nullable(); // Sound file for door opening
            $table->timestamps();
        });

        Schema::create('committee_members', function (Blueprint $table) {
            $table->id();
            $table->foreignId('committee_year_id')->constrained('committee_years')->onDelete('cascade');
            $table->string('name');
            $table->string('role');
            $table->string('avatar')->nullable();
            $table->text('bio')->nullable();
            $table->json('multi_year_roles')->nullable(); // For multi-year members
            $table->boolean('is_legend')->default(false);  // Special legendary status
            $table->timestamps();
        });

        Schema::create('committee_artifacts', function (Blueprint $table) {
            $table->id();
            $table->foreignId('committee_year_id')->constrained('committee_years')->onDelete('cascade');
            $table->string('name');
            $table->string('type'); // trophy, medal, scroll, photo
            $table->string('icon')->nullable();
            $table->string('image')->nullable();
            $table->text('story')->nullable();
            $table->json('position')->nullable(); // x, y position in room
            $table->timestamps();
        });
    }

    public function down()
    {
        Schema::dropIfExists('committee_artifacts');
        Schema::dropIfExists('committee_members');
        Schema::dropIfExists('committee_years');
    }
}
