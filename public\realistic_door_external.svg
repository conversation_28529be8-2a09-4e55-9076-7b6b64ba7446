<?xml version="1.0" encoding="UTF-8"?>
<svg xmlns="http://www.w3.org/2000/svg" width="1024" height="1536" viewBox="0 0 1024 1536" preserveAspectRatio="xMidYMid meet">
  <!-- External-referenced (lightweight) SVG. Put the three files in the same folder as this SVG when serving. -->
  <image href="full_bg.png" x="0" y="0" width="1024" height="1536" preserveAspectRatio="none" />
  <!-- Left door group: the image is positioned where the left door sits in the background -->
  <g id="leftDoor" transform-origin="0% 50%" style="transform-box:fill-box; -webkit-transform-box:fill-box;">
    <image id="leftImg" href="left_panel.png" x="162" y="0" width="352" height="1536" preserveAspectRatio="none" />
  </g>
  <!-- Right door group -->
  <g id="rightDoor" transform-origin="100% 50%" style="transform-box:fill-box; -webkit-transform-box:fill-box;">
    <image id="rightImg" href="right_panel.png" x="514" y="0" width="352" height="1536" preserveAspectRatio="none" />
  </g>
  <!-- invisible hit rect to capture clicks if needed -->
  <rect id="hit" x="0" y="0" width="1024" height="1536" fill="transparent" style="pointer-events:all"/>
</svg>
